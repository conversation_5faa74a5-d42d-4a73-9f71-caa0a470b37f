package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.domain.AppUserClosePayPageRecord;
import tv.shorthub.system.mapper.AppUserClosePayPageRecordMapper;
import tv.shorthub.system.service.AppUserClosePayPageRecordService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 用户关闭支付页面记录Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class AppUserClosePayPageRecordServiceImpl extends BaseService<AppUserClosePayPageRecord> implements AppUserClosePayPageRecordService
{
    @Autowired
    private AppUserClosePayPageRecordMapper appUserClosePayPageRecordMapper;

    @Override
    public AppUserClosePayPageRecordMapper getMapper() {
        return appUserClosePayPageRecordMapper;
    }

    /**
     * 查询用户最新的关闭记录
     * @param userId 用户id
     * @return AppUserClosePayPageRecord
     */
    @Override
    public AppUserClosePayPageRecord queryUserLatestRecordByUserId(String userId) {
        QueryWrapper<AppUserClosePayPageRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByDesc("create_time").last("LIMIT 1");
        return appUserClosePayPageRecordMapper.selectOne(queryWrapper);
    }

    /**
     * 查询用户关闭支付页面记录
     *
     * @param id 用户关闭支付页面记录主键
     * @return 用户关闭支付页面记录
     */
    @Override
    public AppUserClosePayPageRecord getById(Long id)
    {
        return appUserClosePayPageRecordMapper.selectById(id);
    }

    /**
     * 查询用户关闭支付页面记录列表
     *
     * @param query 用户关闭支付页面记录
     * @return 用户关闭支付页面记录
     */
    @Override
    public List<AppUserClosePayPageRecord> selectList(AppUserClosePayPageRecord query)
    {
        return appUserClosePayPageRecordMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询用户关闭支付页面记录数据汇总
     *
     * @param query 用户关闭支付页面记录
     * @return 用户关闭支付页面记录
     */
    @Override
    public AppUserClosePayPageRecord getSummary(AppUserClosePayPageRecord query)
    {
        return appUserClosePayPageRecordMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query 用户关闭支付页面记录
     * @return 用户关闭支付页面记录
     */
    @Override
    public List<AppUserClosePayPageRecord> summary(SummaryRequest query)
    {
        return appUserClosePayPageRecordMapper.summary(query);
    }

    @Override
    public AppUserClosePayPageRecord allSummary(SummaryRequest query)
    {
        return appUserClosePayPageRecordMapper.allSummary(query);
    }

    /**
     * 新增用户关闭支付页面记录
     *
     * @param appUserClosePayPage 用户关闭支付页面记录
     * @return 结果
     */
    @Override
    public int insert(AppUserClosePayPageRecord appUserClosePayPage)
    {
        appUserClosePayPage.setCreateTime(DateUtils.getNowDate());
        return appUserClosePayPageRecordMapper.insert(appUserClosePayPage);
    }

    /**
     * 修改用户关闭支付页面记录
     *
     * @param appUserClosePayPage 用户关闭支付页面记录
     * @return 结果
     */
    @Override
    public int update(AppUserClosePayPageRecord appUserClosePayPage)
    {
        appUserClosePayPage.setUpdateTime(DateUtils.getNowDate());
        return appUserClosePayPageRecordMapper.updateById(appUserClosePayPage);
    }

    /**
     * 批量删除用户关闭支付页面记录
     *
     * @param ids 需要删除的用户关闭支付页面记录主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appUserClosePayPageRecordMapper.deleteByIds(ids);
    }

    /**
     * 删除用户关闭支付页面记录信息
     *
     * @param id 用户关闭支付页面记录主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appUserClosePayPageRecordMapper.deleteById(id);
    }
}