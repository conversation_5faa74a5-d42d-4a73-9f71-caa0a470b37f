package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import java.util.Date;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.common.utils.SecurityUtils;
import tv.shorthub.system.mapper.AppPromotionRequestLogMapper;
import tv.shorthub.system.domain.AppPromotionRequestLog;
import tv.shorthub.system.service.IAppPromotionRequestLogService;
import tv.shorthub.common.core.service.BaseService;

/**
 * promotion访问日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Service
public class AppPromotionRequestLogServiceImpl extends BaseService<AppPromotionRequestLog> implements IAppPromotionRequestLogService
{
    @Autowired
    private AppPromotionRequestLogMapper appPromotionRequestLogMapper;

    @Override
    public AppPromotionRequestLogMapper getMapper() {
        return appPromotionRequestLogMapper;
    }

    /**
     * 查询promotion访问日志
     *
     * @param id promotion访问日志主键
     * @return promotion访问日志
     */
    @Override
    public AppPromotionRequestLog getById(Long id)
    {
        return appPromotionRequestLogMapper.selectById(id);
    }

    /**
     * 查询promotion访问日志列表
     *
     * @param query promotion访问日志
     * @return promotion访问日志
     */
    @Override
    public List<AppPromotionRequestLog> selectList(AppPromotionRequestLog query)
    {
        QueryWrapper<AppPromotionRequestLog> queryWrapper = new QueryWrapper<>(query);
        if (!SecurityUtils.isSystemAdmin()) {
            if (!SecurityUtils.isBusinessAdmin()) {
                return appPromotionRequestLogMapper.selectListByPromotionUser(SecurityUtils.getUsername());
            } else {
                queryWrapper.eq("appid", SecurityUtils.getAppid());
            }
        }
        return appPromotionRequestLogMapper.selectList(queryWrapper.orderByDesc("id"));
    }


    /**
     * 查询promotion访问日志数据汇总
     *
     * @param query promotion访问日志
     * @return promotion访问日志
     */
    @Override
    public AppPromotionRequestLog getSummary(AppPromotionRequestLog query)
    {
        return appPromotionRequestLogMapper.getSummary(query);
    }



    /**
     * 查询自定义分析数据
     *
     * @param query promotion访问日志
     * @return promotion访问日志
     */
    @Override
    public List<AppPromotionRequestLog> summary(SummaryRequest query)
    {
        return appPromotionRequestLogMapper.summary(query);
    }

    @Override
    public AppPromotionRequestLog allSummary(SummaryRequest query)
    {
        return appPromotionRequestLogMapper.allSummary(query);
    }

    /**
     * 新增promotion访问日志
     *
     * @param appPromotionRequestLog promotion访问日志
     * @return 结果
     */
    @Override
    public int insert(AppPromotionRequestLog appPromotionRequestLog)
    {
        appPromotionRequestLog.setCreateTime(DateUtils.getNowDate());
        return appPromotionRequestLogMapper.insert(appPromotionRequestLog);
    }

    /**
     * 修改promotion访问日志
     *
     * @param appPromotionRequestLog promotion访问日志
     * @return 结果
     */
    @Override
    public int update(AppPromotionRequestLog appPromotionRequestLog)
    {
        appPromotionRequestLog.setUpdateTime(DateUtils.getNowDate());
        return appPromotionRequestLogMapper.updateById(appPromotionRequestLog);
    }

    /**
     * 批量删除promotion访问日志
     *
     * @param ids 需要删除的promotion访问日志主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return appPromotionRequestLogMapper.deleteBatchIds(ids);
    }

    /**
     * 删除promotion访问日志信息
     *
     * @param id promotion访问日志主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return appPromotionRequestLogMapper.deleteById(id);
    }

    // Facebook归因相关方法实现
    @Override
    public AppPromotionRequestLog findLatestBySessionId(String sessionId) {
        QueryWrapper<AppPromotionRequestLog> wrapper = new QueryWrapper<>();
        wrapper.eq("sid", sessionId)
               .orderByDesc("create_time")
               .last(" LIMIT 1");
        return appPromotionRequestLogMapper.selectOne(wrapper);
    }

    @Override
    public List<AppPromotionRequestLog> findByIpAndTimeRange(String clientIp, String tfid, long cutoffTime) {
        QueryWrapper<AppPromotionRequestLog> wrapper = new QueryWrapper<>();
        wrapper.eq("ip", clientIp)
               .ge("create_time", new Date(cutoffTime))
               .orderByDesc("create_time");
        if (StringUtils.isNotEmpty(tfid)) {
            wrapper.eq("tid", tfid);
        }
        return appPromotionRequestLogMapper.selectList(wrapper);
    }

    @Override
    public AppPromotionRequestLog findByDeviceId(String deviceId, String tfid) {
        QueryWrapper<AppPromotionRequestLog> wrapper = new QueryWrapper<>();
        wrapper.eq("device_id", deviceId)
               .orderByDesc("create_time")
                .last(" LIMIT 1")
        ;
        if (StringUtils.isNotEmpty(tfid)) {
            wrapper.eq("tid", tfid);
        }
        return appPromotionRequestLogMapper.selectOne(wrapper);
    }
}
