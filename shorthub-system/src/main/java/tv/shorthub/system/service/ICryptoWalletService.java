package tv.shorthub.system.service;

import tv.shorthub.system.mapper.CryptoWalletMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.CryptoWallet;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 虚拟货币钱包地址Service接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface ICryptoWalletService extends IBaseService<CryptoWallet>
{
    /**
     * 查询虚拟货币钱包地址
     *
     * @param id 虚拟货币钱包地址主键
     * @return 虚拟货币钱包地址
     */
    public CryptoWallet getById(Long id);

    /**
     * 查询虚拟货币钱包地址数据汇总
     *
     * @param query 虚拟货币钱包地址
     * @return 虚拟货币钱包地址数据汇总
     */
    public CryptoWallet getSummary(CryptoWallet query);

    /**
     * 查询虚拟货币钱包地址列表
     *
     * @param query 虚拟货币钱包地址
     * @return 虚拟货币钱包地址集合
     */
    public List<CryptoWallet> selectList(CryptoWallet query);

    /**
     * 新增虚拟货币钱包地址
     *
     * @param cryptoWallet 虚拟货币钱包地址
     * @return 结果
     */
    public int insert(CryptoWallet cryptoWallet);

    /**
     * 修改虚拟货币钱包地址
     *
     * @param cryptoWallet 虚拟货币钱包地址
     * @return 结果
     */
    public int update(CryptoWallet cryptoWallet);

    /**
     * 批量删除虚拟货币钱包地址
     *
     * @param ids 需要删除的虚拟货币钱包地址主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除虚拟货币钱包地址信息
     *
     * @param id 虚拟货币钱包地址主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 虚拟货币钱包地址
     * @return 虚拟货币钱包地址集合
     */
    public List<CryptoWallet> summary(SummaryRequest query);

    CryptoWallet allSummary(SummaryRequest query);

    CryptoWalletMapper getMapper();
}
