package tv.shorthub.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tv.shorthub.system.mapper.StatisticsDailyDramaEpisodeStatsMapper;
import tv.shorthub.system.domain.StatisticsDailyDramaEpisodeStats;
import tv.shorthub.system.service.IStatisticsDailyDramaEpisodeStatsService;
import tv.shorthub.common.core.service.BaseService;

/**
 * 剧集每日统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class StatisticsDailyDramaEpisodeStatsServiceImpl extends BaseService<StatisticsDailyDramaEpisodeStats> implements IStatisticsDailyDramaEpisodeStatsService
{
    @Autowired
    private StatisticsDailyDramaEpisodeStatsMapper statisticsDailyDramaEpisodeStatsMapper;

    @Override
    public StatisticsDailyDramaEpisodeStatsMapper getMapper() {
        return statisticsDailyDramaEpisodeStatsMapper;
    }

    /**
     * 查询剧集每日统计
     *
     * @param id 剧集每日统计主键
     * @return 剧集每日统计
     */
    @Override
    public StatisticsDailyDramaEpisodeStats getById(Long id)
    {
        return statisticsDailyDramaEpisodeStatsMapper.selectById(id);
    }

    /**
     * 查询剧集每日统计列表
     *
     * @param query 剧集每日统计
     * @return 剧集每日统计
     */
    @Override
    public List<StatisticsDailyDramaEpisodeStats> selectList(StatisticsDailyDramaEpisodeStats query)
    {
        return statisticsDailyDramaEpisodeStatsMapper.selectList(new QueryWrapper<>(query));
    }


    /**
     * 查询剧集每日统计数据汇总
     *
     * @param query 剧集每日统计
     * @return 剧集每日统计
     */
    @Override
    public StatisticsDailyDramaEpisodeStats getSummary(StatisticsDailyDramaEpisodeStats query)
    {
        return statisticsDailyDramaEpisodeStatsMapper.getSummary(query);
    }

    /**
     * 查询自定义分析数据
     *
     * @param query 剧集每日统计
     * @return 剧集每日统计
     */
    @Override
    public List<StatisticsDailyDramaEpisodeStats> summary(SummaryRequest query)
    {
        return statisticsDailyDramaEpisodeStatsMapper.summary(query);
    }

    @Override
    public StatisticsDailyDramaEpisodeStats allSummary(SummaryRequest query)
    {
        return statisticsDailyDramaEpisodeStatsMapper.allSummary(query);
    }

    /**
     * 新增剧集每日统计
     *
     * @param statisticsDailyDramaEpisodeStats 剧集每日统计
     * @return 结果
     */
    @Override
    public int insert(StatisticsDailyDramaEpisodeStats statisticsDailyDramaEpisodeStats)
    {
        statisticsDailyDramaEpisodeStats.setCreateTime(DateUtils.getNowDate());
        return statisticsDailyDramaEpisodeStatsMapper.insert(statisticsDailyDramaEpisodeStats);
    }

    /**
     * 修改剧集每日统计
     *
     * @param statisticsDailyDramaEpisodeStats 剧集每日统计
     * @return 结果
     */
    @Override
    public int update(StatisticsDailyDramaEpisodeStats statisticsDailyDramaEpisodeStats)
    {
        statisticsDailyDramaEpisodeStats.setUpdateTime(DateUtils.getNowDate());
        return statisticsDailyDramaEpisodeStatsMapper.updateById(statisticsDailyDramaEpisodeStats);
    }

    /**
     * 批量删除剧集每日统计
     *
     * @param ids 需要删除的剧集每日统计主键
     * @return 结果
     */
    @Override
    public int deleteByIds(List<Long> ids)
    {
        return statisticsDailyDramaEpisodeStatsMapper.deleteBatchIds(ids);
    }

    /**
     * 删除剧集每日统计信息
     *
     * @param id 剧集每日统计主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id)
    {
        return statisticsDailyDramaEpisodeStatsMapper.deleteById(id);
    }


}
