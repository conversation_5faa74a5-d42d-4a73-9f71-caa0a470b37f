package tv.shorthub.system.service;

import tv.shorthub.system.mapper.StatisticsDailyDramaEpisodeStatsMapper;
import tv.shorthub.common.core.domain.SummaryRequest;
import java.util.List;
import tv.shorthub.system.domain.StatisticsDailyDramaEpisodeStats;
import tv.shorthub.common.core.service.IBaseService;

/**
 * 剧集每日统计Service接口
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface IStatisticsDailyDramaEpisodeStatsService extends IBaseService<StatisticsDailyDramaEpisodeStats>
{
    /**
     * 查询剧集每日统计
     *
     * @param id 剧集每日统计主键
     * @return 剧集每日统计
     */
    public StatisticsDailyDramaEpisodeStats getById(Long id);

    /**
     * 查询剧集每日统计数据汇总
     *
     * @param query 剧集每日统计
     * @return 剧集每日统计数据汇总
     */
    public StatisticsDailyDramaEpisodeStats getSummary(StatisticsDailyDramaEpisodeStats query);

    /**
     * 查询剧集每日统计列表
     *
     * @param query 剧集每日统计
     * @return 剧集每日统计集合
     */
    public List<StatisticsDailyDramaEpisodeStats> selectList(StatisticsDailyDramaEpisodeStats query);

    /**
     * 新增剧集每日统计
     *
     * @param statisticsDailyDramaEpisodeStats 剧集每日统计
     * @return 结果
     */
    public int insert(StatisticsDailyDramaEpisodeStats statisticsDailyDramaEpisodeStats);

    /**
     * 修改剧集每日统计
     *
     * @param statisticsDailyDramaEpisodeStats 剧集每日统计
     * @return 结果
     */
    public int update(StatisticsDailyDramaEpisodeStats statisticsDailyDramaEpisodeStats);

    /**
     * 批量删除剧集每日统计
     *
     * @param ids 需要删除的剧集每日统计主键集合
     * @return 结果
     */
    public int deleteByIds(List<Long> ids);

    /**
     * 删除剧集每日统计信息
     *
     * @param id 剧集每日统计主键
     * @return 结果
     */
    public int deleteById(Long id);


    /**
     * 查询自定义分析数据
     *
     * @param query 剧集每日统计
     * @return 剧集每日统计集合
     */
    public List<StatisticsDailyDramaEpisodeStats> summary(SummaryRequest query);

    StatisticsDailyDramaEpisodeStats allSummary(SummaryRequest query);

    StatisticsDailyDramaEpisodeStatsMapper getMapper();
}
